import { NextResponse } from 'next/server';
import { logoutAction } from '@/lib/actions';

export async function POST() {
  try {
    const result = await logoutAction();
    
    if (result.success) {
      // Redirect to login page after successful logout
      return NextResponse.redirect(new URL('/login', process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'));
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Logout API error:', error);
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    );
  }
}
