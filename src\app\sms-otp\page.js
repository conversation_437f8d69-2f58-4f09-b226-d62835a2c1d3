'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import OTPForm from '@/components/OTPForm';

export default function SMSOTPPage() {
  const router = useRouter();
  const [resendTimer, setResendTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const [mfaData, setMfaData] = useState(null);

  // Load MFA data from session storage
  useEffect(() => {
    const storedMfaData = sessionStorage.getItem('mfaData');
    if (storedMfaData) {
      setMfaData(JSON.parse(storedMfaData));
    }
  }, []);

  // Timer for resend functionality
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendTimer]);

  const handleOTPSubmit = async (result) => {
    // Handle Shipt MFA verification result
    console.log('Shipt OTP verification result:', result);

    if (result.user) {
      // Clear MFA data
      sessionStorage.removeItem('mfaData');

      // The redirect will be handled automatically by the useShiptAuth hook
      // which will redirect to result.user.redirect_url from backend
      console.log('OTP verified successfully! Redirecting to:', result.user.redirect_url);
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;
    
    try {
      // Placeholder for resend API call
      console.log('Resending OTP...');
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Reset timer
      setResendTimer(30);
      setCanResend(false);
      setError('');
      
      alert('OTP has been resent to your phone number');
    } catch (err) {
      setError('Failed to resend OTP. Please try again.');
    }
  };

  const handleBackToLogin = () => {
    router.push('/login');
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="mt-6 text-center">
          <button
            onClick={handleBackToLogin}
            className="text-sm text-foreground-light hover:text-foreground font-medium transition-colors duration-200 flex items-center justify-center mx-auto"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Login
          </button>
        </div>
        <div className="text-center mb-8 animate-fade-in">
          <div className="mx-auto">
            <img src="/otp.png"/>
          </div>
          <h1 className="heading-primary">
            Verify Your Phone
          </h1>
          <p className="heading-secondary">
            We&apos;ve sent a 6-digit verification code to your phone number
          </p>
          {/* <p className="text-foreground text-sm font-semibold mt-2 bg-gray-50 px-4 py-2 rounded-lg inline-block">
            +1 (***) ***-1234
          </p> */}
        </div>

        <OTPForm
          onSubmit={handleOTPSubmit}
          mfaData={mfaData}
        />

        <div className="mt-8 text-center">
          <p className="text-sm text-foreground-light mb-4">
            Didn&apos;t receive the code?
          </p>

          <button
            onClick={handleResendOTP}
            disabled={!canResend}
            className={`text-sm font-semibold transition-colors duration-200 ${
              canResend
                ? 'text-primary hover:text-primary-dark cursor-pointer'
                : 'text-gray-400 cursor-not-allowed'
            }`}
          >
            {canResend ? 'Resend Code' : `Resend in ${resendTimer}s`}
          </button>
        </div>

      </div>
    </div>
  );
}
