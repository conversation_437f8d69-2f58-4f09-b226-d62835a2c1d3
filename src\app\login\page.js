'use client';

import { useRouter } from 'next/navigation';
import LoginForm from '@/components/LoginForm';

export default function LoginPage() {
  const router = useRouter();

  const handleLogin = async (result) => {
    // Handle Shipt authentication result
    console.log('Shipt login result:', result);

    if (result.requires_otp) {
      // Store MFA data and redirect to OTP page
      sessionStorage.setItem('mfaData', JSON.stringify({
        channel_id: result.channel_id,
        phone_number: result.phone_number
      }));

      const urlParams = new URLSearchParams(window.location.search);
      const userId = urlParams.get('user_id');
      router.push('/sms-otp?user_id=' + userId);
    } else if (result.user) {
      // Login successful without OTP
      // The redirect will be handled automatically by the useShiptAuth hook
      // which will redirect to result.user.redirect_url from backend
      console.log('Login successful:', result.user);
      console.log('Redirecting to:', result.user.redirect_url);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <LoginForm
          onSubmit={handleLogin}
        />
      </div>
    </div>
  );
}
