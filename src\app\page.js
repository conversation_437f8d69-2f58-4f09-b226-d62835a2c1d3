import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/actions';

export default async function Home() {
  // Check if user is authenticated
  const user = await getCurrentUser();

  if (!user) {
    // Redirect to login if not authenticated
    redirect('/login');
  }

  // If authenticated, show dashboard
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome to Your Dashboard
          </h1>
          <p className="text-gray-600 mb-6">
            You have successfully logged in and verified your account.
          </p>

          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Account Status</h3>
              <p className="text-sm text-green-600">✓ Verified</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Security</h3>
              <p className="text-sm text-green-600">✓ Two-Factor Authentication Enabled</p>
            </div>
          </div>

          <div className="mt-6">
            <form action="/api/logout" method="POST">
              <button
                type="submit"
                className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
              >
                Sign Out
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
