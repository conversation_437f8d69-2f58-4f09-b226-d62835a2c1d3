// Validation utilities for form inputs

// Email validation regex
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Phone number validation regex (supports international formats)
const PHONE_REGEX = /^[\+]?[1-9][\d]{0,15}$/;

// Strong password regex (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
const STRONG_PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;

// OTP validation regex (6 digits)
const OTP_REGEX = /^\d{6}$/;

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {object} - Validation result
 */
export function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return {
      isValid: false,
      error: 'Email is required'
    };
  }

  const trimmedEmail = email.trim();
  
  if (trimmedEmail.length === 0) {
    return {
      isValid: false,
      error: 'Email is required'
    };
  }

  if (trimmedEmail.length > 254) {
    return {
      isValid: false,
      error: 'Email is too long'
    };
  }

  if (!EMAIL_REGEX.test(trimmedEmail)) {
    return {
      isValid: false,
      error: 'Please enter a valid email address'
    };
  }

  return {
    isValid: true,
    value: trimmedEmail.toLowerCase()
  };
}

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @returns {object} - Validation result
 */
export function validatePhone(phone) {
  if (!phone || typeof phone !== 'string') {
    return {
      isValid: false,
      error: 'Phone number is required'
    };
  }

  // Remove all non-digit characters except +
  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  if (cleanPhone.length === 0) {
    return {
      isValid: false,
      error: 'Phone number is required'
    };
  }

  if (!PHONE_REGEX.test(cleanPhone)) {
    return {
      isValid: false,
      error: 'Please enter a valid phone number'
    };
  }

  return {
    isValid: true,
    value: cleanPhone
  };
}

/**
 * Validate email or phone number
 * @param {string} emailOrPhone - Email or phone to validate
 * @returns {object} - Validation result
 */
export function validateEmailOrPhone(emailOrPhone) {
  if (!emailOrPhone || typeof emailOrPhone !== 'string') {
    return {
      isValid: false,
      error: 'Email or phone number is required'
    };
  }

  const trimmed = emailOrPhone.trim();
  
  if (trimmed.length === 0) {
    return {
      isValid: false,
      error: 'Email or phone number is required'
    };
  }

  // Try email validation first
  const emailValidation = validateEmail(trimmed);
  if (emailValidation.isValid) {
    return {
      isValid: true,
      value: emailValidation.value,
      type: 'email'
    };
  }

  // Try phone validation
  const phoneValidation = validatePhone(trimmed);
  if (phoneValidation.isValid) {
    return {
      isValid: true,
      value: phoneValidation.value,
      type: 'phone'
    };
  }

  return {
    isValid: false,
    error: 'Please enter a valid email address or phone number'
  };
}

/**
 * Validate password
 * @param {string} password - Password to validate
 * @param {object} options - Validation options
 * @returns {object} - Validation result
 */
export function validatePassword(password, options = {}) {
  const {
    minLength = 6,
    requireStrong = false,
    allowEmpty = false
  } = options;

  if (!password || typeof password !== 'string') {
    if (allowEmpty) {
      return { isValid: true, value: '' };
    }
    return {
      isValid: false,
      error: 'Password is required'
    };
  }

  if (password.length === 0 && allowEmpty) {
    return { isValid: true, value: '' };
  }

  if (password.length < minLength) {
    return {
      isValid: false,
      error: `Password must be at least ${minLength} characters long`
    };
  }

  if (password.length > 128) {
    return {
      isValid: false,
      error: 'Password is too long (maximum 128 characters)'
    };
  }

  if (requireStrong && !STRONG_PASSWORD_REGEX.test(password)) {
    return {
      isValid: false,
      error: 'Password must contain at least 8 characters, including uppercase, lowercase, and a number'
    };
  }

  return {
    isValid: true,
    value: password
  };
}

/**
 * Validate OTP code
 * @param {string} otp - OTP to validate
 * @returns {object} - Validation result
 */
export function validateOTP(otp) {
  if (!otp || typeof otp !== 'string') {
    return {
      isValid: false,
      error: 'OTP is required'
    };
  }

  const trimmedOTP = otp.trim();
  
  if (trimmedOTP.length === 0) {
    return {
      isValid: false,
      error: 'OTP is required'
    };
  }

  if (!OTP_REGEX.test(trimmedOTP)) {
    return {
      isValid: false,
      error: 'Please enter a valid 6-digit OTP'
    };
  }

  return {
    isValid: true,
    value: trimmedOTP
  };
}

/**
 * Validate login form data
 * @param {object} formData - Form data to validate
 * @returns {object} - Validation result
 */
export function validateLoginForm(formData) {
  const errors = {};
  const validatedData = {};

  // Validate email or phone
  const emailOrPhoneValidation = validateEmailOrPhone(formData.emailOrPhone);
  if (!emailOrPhoneValidation.isValid) {
    errors.emailOrPhone = emailOrPhoneValidation.error;
  } else {
    validatedData.emailOrPhone = emailOrPhoneValidation.value;
    validatedData.inputType = emailOrPhoneValidation.type;
  }

  // Validate password
  const passwordValidation = validatePassword(formData.password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.error;
  } else {
    validatedData.password = passwordValidation.value;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: validatedData
  };
}

/**
 * Validate OTP form data
 * @param {object} formData - Form data to validate
 * @returns {object} - Validation result
 */
export function validateOTPForm(formData) {
  const errors = {};
  const validatedData = {};

  // Validate OTP
  const otpValidation = validateOTP(formData.otp);
  if (!otpValidation.isValid) {
    errors.otp = otpValidation.error;
  } else {
    validatedData.otp = otpValidation.value;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: validatedData
  };
}

/**
 * Sanitize input string
 * @param {string} input - Input to sanitize
 * @returns {string} - Sanitized input
 */
export function sanitizeInput(input) {
  if (!input || typeof input !== 'string') {
    return '';
  }
  
  return input.trim().replace(/[<>]/g, '');
}
