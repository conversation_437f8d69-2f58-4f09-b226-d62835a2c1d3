'use client';

import { useEffect, useState } from 'react';

// Mobile optimization utilities and components

/**
 * Hook to prevent zoom on iOS when focusing inputs
 */
export function usePreventZoom() {
  useEffect(() => {
    const handleTouchStart = (e) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    const handleTouchEnd = (e) => {
      const now = new Date().getTime();
      if (now - lastTouchEnd <= 300) {
        e.preventDefault();
      }
      lastTouchEnd = now;
    };

    let lastTouchEnd = 0;

    document.addEventListener('touchstart', handleTouchStart, { passive: false });
    document.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, []);
}

/**
 * Hook to handle virtual keyboard on mobile
 */
export function useVirtualKeyboard() {
  useEffect(() => {
    const handleResize = () => {
      // Detect virtual keyboard
      const isKeyboardOpen = window.innerHeight < window.screen.height * 0.75;
      
      if (isKeyboardOpen) {
        document.body.classList.add('keyboard-open');
      } else {
        document.body.classList.remove('keyboard-open');
      }
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      document.body.classList.remove('keyboard-open');
    };
  }, []);
}

/**
 * Mobile-optimized input component
 */
export function MobileInput({ 
  type = 'text', 
  inputMode, 
  autoComplete,
  className = '',
  onFocus,
  onBlur,
  ...props 
}) {
  const handleFocus = (e) => {
    // Scroll input into view on mobile
    if (window.innerWidth <= 640) {
      setTimeout(() => {
        e.target.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }, 300);
    }
    
    if (onFocus) onFocus(e);
  };

  const handleBlur = (e) => {
    // Scroll back to top on mobile after input blur
    if (window.innerWidth <= 640) {
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    }
    
    if (onBlur) onBlur(e);
  };

  return (
    <input
      type={type}
      inputMode={inputMode}
      autoComplete={autoComplete}
      className={`input-field ${className}`}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...props}
    />
  );
}

/**
 * Mobile-optimized button component
 */
export function MobileButton({ 
  children, 
  className = '', 
  disabled = false,
  onClick,
  ...props 
}) {
  const handleClick = (e) => {
    // Add haptic feedback on supported devices
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }
    
    if (onClick) onClick(e);
  };

  return (
    <button
      className={`btn-primary ${className}`}
      disabled={disabled}
      onClick={handleClick}
      {...props}
    >
      {children}
    </button>
  );
}

/**
 * Mobile-optimized OTP input component
 */
export function MobileOTPInput({ 
  value, 
  onChange, 
  onKeyDown, 
  onPaste,
  index,
  disabled = false,
  inputRef,
  ...props 
}) {
  const handleFocus = (e) => {
    // Select all text on focus for better UX
    e.target.select();
    
    // Scroll into view on mobile
    if (window.innerWidth <= 640) {
      setTimeout(() => {
        e.target.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }, 100);
    }
  };

  return (
    <input
      ref={inputRef}
      type="text"
      inputMode="numeric"
      pattern="[0-9]*"
      maxLength={1}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
      onPaste={onPaste}
      onFocus={handleFocus}
      className="otp-input"
      disabled={disabled}
      autoComplete="off"
      autoCorrect="off"
      autoCapitalize="off"
      spellCheck="false"
      {...props}
    />
  );
}

/**
 * Component to handle mobile-specific layout adjustments
 */
export function MobileLayout({ children }) {
  usePreventZoom();
  useVirtualKeyboard();

  return (
    <div className="mobile-layout">
      {children}
      <style jsx>{`
        .mobile-layout {
          min-height: 100vh;
          min-height: 100dvh; /* Dynamic viewport height */
        }
        
        @media (max-width: 640px) {
          :global(.keyboard-open) {
            height: 100vh;
            overflow: hidden;
          }
          
          :global(.keyboard-open) .mobile-layout {
            height: 100vh;
            overflow-y: auto;
          }
        }
      `}</style>
    </div>
  );
}

/**
 * Hook to detect if user is on mobile device
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 640);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}

/**
 * Hook to handle safe area insets
 */
export function useSafeArea() {
  useEffect(() => {
    const updateSafeArea = () => {
      const safeAreaTop = getComputedStyle(document.documentElement)
        .getPropertyValue('env(safe-area-inset-top)') || '0px';
      const safeAreaBottom = getComputedStyle(document.documentElement)
        .getPropertyValue('env(safe-area-inset-bottom)') || '0px';

      document.documentElement.style.setProperty('--safe-area-top', safeAreaTop);
      document.documentElement.style.setProperty('--safe-area-bottom', safeAreaBottom);
    };

    updateSafeArea();
    window.addEventListener('resize', updateSafeArea);
    window.addEventListener('orientationchange', updateSafeArea);

    return () => {
      window.removeEventListener('resize', updateSafeArea);
      window.removeEventListener('orientationchange', updateSafeArea);
    };
  }, []);
}
