'use client';

import { useState } from 'react';
import { useShiptAuth, formUtils } from '@/lib/shipt-api-adapter';
import { ErrorMessage } from './MessageContainer';

export default function LoginForm({ onSubmit }) {
  const [formData, setFormData] = useState({
    emailOrPhone: '',
    password: ''
  });
  const [validationErrors, setValidationErrors] = useState({});

  // Use Shipt authentication patterns
  const shiptAuth = useShiptAuth();

  const validateForm = () => {
    // Use Shipt validation
    const validation = formUtils.validateLoginForm(formData);
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        // Use Shipt authentication pattern
        const transformedData = formUtils.transformLoginData(formData);
        const result = await shiptAuth.sendMfa(transformedData);

        // Call parent onSubmit with result
        if (onSubmit) {
          onSubmit(result);
        }
      } catch (error) {
        // Error is handled by shiptAuth hook
        console.error('Shipt login error:', error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-8">
        {/* Logo */}
        <div className="logo mb-4">
          <svg id="logosandtypes_com" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 150"><path d="M0 0h150v150H0V0z" fill="none"/><path d="M77.6 140.6c-13.8 0-27.4-2.6-40.2-7.6-6.6-2.6-10.7-9.2-10.2-16.3L30.8 58l17.9 1.1-3.5 57.7c20.9 7.9 44 7.9 64.9-.1-.4-6.5-2.7-40.5-4.2-60.4-17.9 9-33 9.7-44.1 1.9-7-5-11.1-13-11-21.6-.1-9 4.2-17.4 11.5-22.7 6.4-4.7 14.6-6.2 22.2-4.1 8.5 2.3 15.9 8.8 19.9 17.4l-16.3 7.5c-1.7-3.8-4.9-6.7-8.2-7.6-2.5-.7-5.2-.1-7.3 1.5-2.5 1.9-4 4.9-3.9 8-.1 2.7 1.2 5.4 3.4 7 6.8 4.8 20.6 1.1 36.8-9.7 2.8-1.9 6.5-2 9.4-.3 4.2 2.4 4.4 5.8 4.7 10.4l5 72.6c.6 7.1-3.6 13.8-10.3 16.4-12.7 5-26.4 7.6-40.1 7.6z" fill="#23cc6b"/></svg>
        </div>
        <h1 className="heading-primary">Shipt</h1>
        <h2 className="text-lg font-medium text-gray-600 mb-4">Shopper</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6 text-left">
        {shiptAuth.error && (
          <ErrorMessage message={shiptAuth.error?.message} />
        )}

        <div>
          <label
            htmlFor="emailOrPhone"
            className="label-text"
          >
            Email
          </label>
          <input
            id="emailOrPhone"
            name="emailOrPhone"
            type="email"
            autoComplete="username"
            required
            value={formData.emailOrPhone}
            onChange={handleInputChange}
            className={`input-field ${validationErrors.emailOrPhone ? 'error' : ''}`}
            placeholder="Enter your Email"
            disabled={shiptAuth.loading}
          />
          {validationErrors.emailOrPhone && (
            <p className="error-message animate-slide-in">{validationErrors.emailOrPhone}</p>
          )}
        </div>

        <div>
          <label
            htmlFor="password"
            className="label-text"
          >
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            value={formData.password}
            onChange={handleInputChange}
            className={`input-field ${validationErrors.password ? 'error' : ''}`}
            placeholder="Enter your password"
            disabled={shiptAuth.loading}
          />
          {validationErrors.password && (
            <p className="error-message animate-slide-in">{validationErrors.password}</p>
          )}
        </div>

        {/* <div className="text-center">
          <a href="#" className="text-sm text-primary font-medium hover:text-primary-dark">
            Forgot password?
          </a>
        </div> */}

        <div>
          <button
            type="submit"
            disabled={shiptAuth.loading}
            className="btn-primary"
          >
            {shiptAuth.loading ? (
              <div className="flex items-center justify-center">
                <div className="spinner" style={{marginRight: '0.5rem'}}></div>
                <span className="ml-3">Signing in...</span>
              </div>
            ) : (
              'Start shopping'
            )}
          </button>
        </div>
      </form>

      {/* Footer links */}
      <div className="mt-8 text-center">
        <div className="mt-6 space-y-2">
          <p className="text-xs text-gray-500">
            By logging in, you agree to{' '}
            <a href="#" className="text-primary hover:text-primary-dark">Terms of Service</a>
          </p>
          <p className="text-xs text-gray-500">
            <a href="#" className="text-primary hover:text-primary-dark">Shopper Privacy Notice</a>
          </p>
          <p className="text-xs text-gray-500">
            <a href="#" className="text-primary hover:text-primary-dark">CA Privacy Rights</a>
          </p>
          <p className="text-xs text-gray-500">
            <a href="#" className="text-primary hover:text-primary-dark">Do Not Sell My Personal Information</a>
          </p>
        </div>
      </div>
    </div>
  );
}
