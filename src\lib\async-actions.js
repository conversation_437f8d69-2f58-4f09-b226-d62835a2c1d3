// Async action pattern adapted from <PERSON><PERSON>'s Redux approach for React state management
// Provides standardized state management for async operations

import { useState, useCallback, useRef } from 'react';
import { apiClient, APIError, API_ERROR_TYPES } from './api-client';

/**
 * Action states similar to Shipt's REQUEST/SUCCESS/FAILURE pattern
 */
export const ACTION_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
};

/**
 * Create async action hook similar to Shipt's async action creators
 * This provides a standardized way to handle async operations with loading states
 */
export function useAsyncAction(actionKey, serverAction, options = {}) {
  const [state, setState] = useState({
    status: ACTION_STATES.IDLE,
    data: null,
    error: null,
    loading: false
  });

  const abortControllerRef = useRef(null);

  const execute = useCallback(async (data = null, executeOptions = {}) => {
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setState(prev => ({
        ...prev,
        status: ACTION_STATES.LOADING,
        loading: true,
        error: null
      }));

      const result = await apiClient.executeAction(
        actionKey,
        serverAction,
        data,
        { ...options, ...executeOptions, signal: abortControllerRef.current.signal }
      );

      setState({
        status: ACTION_STATES.SUCCESS,
        data: result.data,
        error: null,
        loading: false
      });

      return result;
    } catch (error) {
      if (error.name === 'AbortError') {
        // Request was cancelled, don't update state
        return;
      }

      setState({
        status: ACTION_STATES.ERROR,
        data: null,
        error: error,
        loading: false
      });

      throw error;
    }
  }, [actionKey, serverAction, options]);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState({
      status: ACTION_STATES.IDLE,
      data: null,
      error: null,
      loading: false
    });
  }, []);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    ...state,
    execute,
    reset,
    cancel,
    isIdle: state.status === ACTION_STATES.IDLE,
    isLoading: state.status === ACTION_STATES.LOADING,
    isSuccess: state.status === ACTION_STATES.SUCCESS,
    isError: state.status === ACTION_STATES.ERROR
  };
}

/**
 * Multi-step async action hook for complex flows like login -> MFA
 * Similar to Shipt's multi-step authentication pattern
 */
export function useMultiStepAction(steps, options = {}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [stepData, setStepData] = useState({});
  const [globalState, setGlobalState] = useState({
    status: ACTION_STATES.IDLE,
    error: null,
    loading: false
  });

  const executeStep = useCallback(async (stepIndex, data = null) => {
    if (stepIndex >= steps.length) {
      throw new Error(`Step ${stepIndex} does not exist`);
    }

    const step = steps[stepIndex];
    
    try {
      setGlobalState(prev => ({
        ...prev,
        status: ACTION_STATES.LOADING,
        loading: true,
        error: null
      }));

      const result = await apiClient.executeAction(
        `${step.key}_step_${stepIndex}`,
        step.action,
        data,
        { ...options, ...step.options }
      );

      // Store step data for potential use in subsequent steps
      setStepData(prev => ({
        ...prev,
        [stepIndex]: result.data
      }));

      setGlobalState({
        status: ACTION_STATES.SUCCESS,
        error: null,
        loading: false
      });

      return result;
    } catch (error) {
      setGlobalState({
        status: ACTION_STATES.ERROR,
        error: error,
        loading: false
      });

      throw error;
    }
  }, [steps, options]);

  const nextStep = useCallback(async (data = null) => {
    const result = await executeStep(currentStep, data);
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
    
    return result;
  }, [currentStep, executeStep, steps.length]);

  const goToStep = useCallback((stepIndex) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStep(stepIndex);
    }
  }, [steps.length]);

  const reset = useCallback(() => {
    setCurrentStep(0);
    setStepData({});
    setGlobalState({
      status: ACTION_STATES.IDLE,
      error: null,
      loading: false
    });
  }, []);

  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;
  const currentStepConfig = steps[currentStep];

  return {
    currentStep,
    currentStepConfig,
    stepData,
    globalState,
    isLastStep,
    isFirstStep,
    executeStep,
    nextStep,
    goToStep,
    reset,
    isLoading: globalState.loading,
    error: globalState.error
  };
}

/**
 * Form action hook that combines form handling with async actions
 * Similar to Shipt's form submission pattern
 */
export function useFormAction(actionKey, serverAction, options = {}) {
  const asyncAction = useAsyncAction(actionKey, serverAction, options);
  const [formErrors, setFormErrors] = useState({});

  const submitForm = useCallback(async (formData, formOptions = {}) => {
    try {
      // Clear previous form errors
      setFormErrors({});

      const result = await asyncAction.execute(formData, formOptions);
      
      // Call success callback if provided
      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error) {
      // Handle validation errors
      if (error.type === API_ERROR_TYPES.VALIDATION && error.details.fields) {
        setFormErrors(error.details.fields);
      }

      // Call error callback if provided
      if (options.onError) {
        options.onError(error);
      }

      throw error;
    }
  }, [asyncAction, options]);

  const clearFormErrors = useCallback(() => {
    setFormErrors({});
  }, []);

  return {
    ...asyncAction,
    formErrors,
    submitForm,
    clearFormErrors,
    hasFormErrors: Object.keys(formErrors).length > 0
  };
}
