// Enhanced error handling system adapted from <PERSON><PERSON>'s error handling patterns
// Provides centralized error handling with user notifications and proper categorization

import { useState, useEffect } from 'react';
import { API_ERROR_TYPES, APIError } from './api-client';

/**
 * Error severity levels for different types of errors
 */
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * User-friendly error messages for different error types
 */
export const ERROR_MESSAGES = {
  [API_ERROR_TYPES.VALIDATION]: {
    title: 'Validation Error',
    message: 'Please check your input and try again.',
    severity: ERROR_SEVERITY.LOW
  },
  [API_ERROR_TYPES.AUTHENTICATION]: {
    title: 'Authentication Required',
    message: 'Please log in to continue.',
    severity: ERROR_SEVERITY.MEDIUM
  },
  [API_ERROR_TYPES.AUTHORIZATION]: {
    title: 'Access Denied',
    message: 'You do not have permission to perform this action.',
    severity: ERROR_SEVERITY.MEDIUM
  },
  [API_ERROR_TYPES.NETWORK]: {
    title: 'Connection Error',
    message: 'Please check your internet connection and try again.',
    severity: ERROR_SEVERITY.MEDIUM
  },
  [API_ERROR_TYPES.SERVER]: {
    title: 'Server Error',
    message: 'Something went wrong on our end. Please try again later.',
    severity: ERROR_SEVERITY.HIGH
  },
  [API_ERROR_TYPES.TIMEOUT]: {
    title: 'Request Timeout',
    message: 'The request took too long. Please try again.',
    severity: ERROR_SEVERITY.MEDIUM
  },
  [API_ERROR_TYPES.UNKNOWN]: {
    title: 'Unexpected Error',
    message: 'An unexpected error occurred. Please try again.',
    severity: ERROR_SEVERITY.HIGH
  }
};

/**
 * Notification system for displaying errors to users
 * Similar to Shipt's snackbar notifications
 */
class NotificationSystem {
  constructor() {
    this.notifications = [];
    this.listeners = [];
    this.nextId = 1;
  }

  /**
   * Add a notification
   */
  add(notification) {
    const id = this.nextId++;
    const fullNotification = {
      id,
      timestamp: Date.now(),
      ...notification
    };

    this.notifications.push(fullNotification);
    this.notifyListeners();

    // Auto-remove after timeout if specified
    if (notification.autoRemove !== false) {
      const timeout = notification.timeout || this.getDefaultTimeout(notification.severity);
      setTimeout(() => this.remove(id), timeout);
    }

    return id;
  }

  /**
   * Remove a notification
   */
  remove(id) {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.notifyListeners();
  }

  /**
   * Clear all notifications
   */
  clear() {
    this.notifications = [];
    this.notifyListeners();
  }

  /**
   * Subscribe to notification changes
   */
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of changes
   */
  notifyListeners() {
    this.listeners.forEach(listener => listener(this.notifications));
  }

  /**
   * Get default timeout based on severity
   */
  getDefaultTimeout(severity) {
    switch (severity) {
      case ERROR_SEVERITY.LOW:
        return 3000;
      case ERROR_SEVERITY.MEDIUM:
        return 5000;
      case ERROR_SEVERITY.HIGH:
        return 8000;
      case ERROR_SEVERITY.CRITICAL:
        return 0; // No auto-remove
      default:
        return 5000;
    }
  }

  /**
   * Show error notification
   */
  error(message, options = {}) {
    return this.add({
      type: 'error',
      message,
      severity: ERROR_SEVERITY.MEDIUM,
      ...options
    });
  }

  /**
   * Show success notification
   */
  success(message, options = {}) {
    return this.add({
      type: 'success',
      message,
      severity: ERROR_SEVERITY.LOW,
      ...options
    });
  }

  /**
   * Show warning notification
   */
  warning(message, options = {}) {
    return this.add({
      type: 'warning',
      message,
      severity: ERROR_SEVERITY.MEDIUM,
      ...options
    });
  }

  /**
   * Show info notification
   */
  info(message, options = {}) {
    return this.add({
      type: 'info',
      message,
      severity: ERROR_SEVERITY.LOW,
      ...options
    });
  }
}

// Create global notification system instance
export const notifications = new NotificationSystem();

/**
 * Enhanced error handler class
 */
export class ErrorHandler {
  constructor(options = {}) {
    this.options = {
      enableLogging: process.env.NODE_ENV === 'development',
      enableNotifications: true,
      enableReporting: process.env.NODE_ENV === 'production',
      ...options
    };

    this.errorReporters = [];
  }

  /**
   * Add error reporter (e.g., Sentry, LogRocket)
   */
  addReporter(reporter) {
    this.errorReporters.push(reporter);
  }

  /**
   * Handle an error with full processing
   */
  handle(error, context = {}) {
    const processedError = this.processError(error, context);
    
    // Log error if enabled
    if (this.options.enableLogging) {
      this.logError(processedError, context);
    }

    // Show notification if enabled
    if (this.options.enableNotifications) {
      this.showNotification(processedError, context);
    }

    // Report error if enabled
    if (this.options.enableReporting) {
      this.reportError(processedError, context);
    }

    return processedError;
  }

  /**
   * Process and normalize error
   */
  processError(error, context = {}) {
    if (error instanceof APIError) {
      return error;
    }

    // Convert regular errors to APIError
    if (error instanceof Error) {
      return new APIError(
        error.message,
        this.inferErrorType(error, context),
        null,
        { originalError: error, context }
      );
    }

    // Handle string errors
    if (typeof error === 'string') {
      return new APIError(error, API_ERROR_TYPES.UNKNOWN, null, { context });
    }

    // Handle unknown error types
    return new APIError(
      'An unknown error occurred',
      API_ERROR_TYPES.UNKNOWN,
      null,
      { originalError: error, context }
    );
  }

  /**
   * Infer error type from error object and context
   */
  inferErrorType(error, context) {
    // Check for network errors
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      return API_ERROR_TYPES.NETWORK;
    }

    // Check for timeout errors
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return API_ERROR_TYPES.TIMEOUT;
    }

    // Check for validation errors
    if (error.name === 'ValidationError' || context.isValidation) {
      return API_ERROR_TYPES.VALIDATION;
    }

    // Check for auth errors
    if (error.message.includes('auth') || error.message.includes('login')) {
      return API_ERROR_TYPES.AUTHENTICATION;
    }

    return API_ERROR_TYPES.UNKNOWN;
  }

  /**
   * Log error to console
   */
  logError(error, context) {
    const logData = {
      message: error.message,
      type: error.type,
      code: error.code,
      details: error.details,
      context,
      stack: error.stack,
      timestamp: error.timestamp
    };

    console.error('[ErrorHandler]', logData);
  }

  /**
   * Show user notification for error
   */
  showNotification(error, context) {
    const errorConfig = ERROR_MESSAGES[error.type] || ERROR_MESSAGES[API_ERROR_TYPES.UNKNOWN];
    
    // Use custom message if provided, otherwise use default
    const message = error.message || errorConfig.message;
    
    notifications.error(message, {
      title: errorConfig.title,
      severity: errorConfig.severity,
      details: error.details,
      context
    });
  }

  /**
   * Report error to external services
   */
  reportError(error, context) {
    this.errorReporters.forEach(reporter => {
      try {
        reporter.report(error, context);
      } catch (reporterError) {
        console.error('Error reporter failed:', reporterError);
      }
    });
  }
}

// Create global error handler instance
export const errorHandler = new ErrorHandler();

/**
 * React hook for using the notification system
 */
export function useNotifications() {
  const [notificationList, setNotificationList] = useState([]);

  useEffect(() => {
    const unsubscribe = notifications.subscribe(setNotificationList);
    return unsubscribe;
  }, []);

  return {
    notifications: notificationList,
    add: notifications.add.bind(notifications),
    remove: notifications.remove.bind(notifications),
    clear: notifications.clear.bind(notifications),
    error: notifications.error.bind(notifications),
    success: notifications.success.bind(notifications),
    warning: notifications.warning.bind(notifications),
    info: notifications.info.bind(notifications)
  };
}

/**
 * React hook for error handling
 */
export function useErrorHandler() {
  return {
    handle: errorHandler.handle.bind(errorHandler),
    handleAuth: errorHandler.handleAuthError?.bind(errorHandler),
    handleValidation: errorHandler.handleValidationError?.bind(errorHandler)
  };
}
