// Enhanced API client system adapted from Shipt's RestAuthHelper pattern
// Designed for Next.js server actions with client-side state management

/**
 * API Response wrapper that standardizes all API responses
 */
export class APIResponse {
  constructor(success, data = null, error = null, meta = {}) {
    this.success = success;
    this.data = data;
    this.error = error;
    this.meta = meta;
    this.timestamp = new Date().toISOString();
  }

  static success(data, meta = {}) {
    return new APIResponse(true, data, null, meta);
  }

  static error(error, meta = {}) {
    return new APIResponse(false, null, error, meta);
  }
}

/**
 * Error types for consistent error handling
 */
export const API_ERROR_TYPES = {
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  NETWORK: 'network',
  SERVER: 'server',
  TIMEOUT: 'timeout',
  UNKNOWN: 'unknown'
};

/**
 * Standardized API error class
 */
export class APIError extends Error {
  constructor(message, type = API_ERROR_TYPES.UNKNOWN, code = null, details = {}) {
    super(message);
    this.name = 'APIError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }

  static fromResponse(response, defaultMessage = 'An error occurred') {
    const message = response.error?.message || defaultMessage;
    const type = response.error?.type || API_ERROR_TYPES.UNKNOWN;
    const code = response.error?.code || null;
    const details = response.error?.details || {};
    
    return new APIError(message, type, code, details);
  }
}

/**
 * Request state management for tracking API calls
 */
export class RequestState {
  constructor() {
    this.loading = false;
    this.error = null;
    this.lastRequest = null;
    this.retryCount = 0;
  }

  setLoading(loading) {
    this.loading = loading;
    if (loading) {
      this.error = null;
    }
  }

  setError(error) {
    this.error = error;
    this.loading = false;
  }

  setSuccess() {
    this.error = null;
    this.loading = false;
    this.retryCount = 0;
  }

  incrementRetry() {
    this.retryCount++;
  }

  reset() {
    this.loading = false;
    this.error = null;
    this.lastRequest = null;
    this.retryCount = 0;
  }
}

/**
 * Enhanced API client that wraps server actions with client-side state management
 * Adapted from Shipt's RestAuthHelper pattern
 */
export class APIClient {
  constructor(options = {}) {
    this.options = {
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      enableLogging: process.env.NODE_ENV === 'development',
      ...options
    };
    
    this.requestStates = new Map();
    this.interceptors = {
      request: [],
      response: [],
      error: []
    };
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor) {
    this.interceptors.request.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor) {
    this.interceptors.response.push(interceptor);
  }

  /**
   * Add error interceptor
   */
  addErrorInterceptor(interceptor) {
    this.interceptors.error.push(interceptor);
  }

  /**
   * Get or create request state for a given key
   */
  getRequestState(key) {
    if (!this.requestStates.has(key)) {
      this.requestStates.set(key, new RequestState());
    }
    return this.requestStates.get(key);
  }

  /**
   * Log API calls in development
   */
  log(level, message, data = {}) {
    if (this.options.enableLogging) {
      console[level](`[APIClient] ${message}`, data);
    }
  }

  /**
   * Apply request interceptors
   */
  async applyRequestInterceptors(requestData) {
    let data = requestData;
    for (const interceptor of this.interceptors.request) {
      data = await interceptor(data);
    }
    return data;
  }

  /**
   * Apply response interceptors
   */
  async applyResponseInterceptors(response) {
    let data = response;
    for (const interceptor of this.interceptors.response) {
      data = await interceptor(data);
    }
    return data;
  }

  /**
   * Apply error interceptors
   */
  async applyErrorInterceptors(error) {
    let err = error;
    for (const interceptor of this.interceptors.error) {
      err = await interceptor(err);
    }
    return err;
  }

  /**
   * Execute a server action with enhanced error handling and state management
   */
  async executeAction(actionKey, serverAction, data = null, options = {}) {
    const requestState = this.getRequestState(actionKey);
    const actionOptions = { ...this.options, ...options };

    this.log('info', `Starting request: ${actionKey}`, { data, options: actionOptions });
    
    try {
      // Apply request interceptors
      const processedData = await this.applyRequestInterceptors(data);
      
      // Set loading state
      requestState.setLoading(true);
      requestState.lastRequest = { actionKey, data: processedData, timestamp: Date.now() };

      // Execute the server action
      const startTime = Date.now();
      const result = await this.executeWithRetry(serverAction, processedData, actionOptions);
      const duration = Date.now() - startTime;

      this.log('info', `Request completed: ${actionKey}`, { duration, success: result.success });

      // Apply response interceptors
      const processedResult = await this.applyResponseInterceptors(result);

      if (processedResult.success) {
        requestState.setSuccess();
        return processedResult;
      } else {
        const error = APIError.fromResponse(processedResult);
        requestState.setError(error);
        
        // Apply error interceptors
        const processedError = await this.applyErrorInterceptors(error);
        throw processedError;
      }
    } catch (error) {
      this.log('error', `Request failed: ${actionKey}`, { error: error.message });
      
      const apiError = error instanceof APIError ? error : new APIError(
        error.message || 'Unknown error occurred',
        API_ERROR_TYPES.UNKNOWN
      );
      
      requestState.setError(apiError);
      
      // Apply error interceptors
      const processedError = await this.applyErrorInterceptors(apiError);
      throw processedError;
    }
  }

  /**
   * Execute server action with retry logic
   */
  async executeWithRetry(serverAction, data, options) {
    let lastError;
    
    for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          this.log('info', `Retry attempt ${attempt}/${options.maxRetries}`);
          await this.delay(options.retryDelay * attempt);
        }

        const result = await serverAction(data);
        return result;
      } catch (error) {
        lastError = error;
        
        // Don't retry on certain error types
        if (error.type === API_ERROR_TYPES.VALIDATION || 
            error.type === API_ERROR_TYPES.AUTHENTICATION) {
          break;
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Utility method for delays
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear all request states
   */
  clearStates() {
    this.requestStates.clear();
  }

  /**
   * Get loading state for a specific action
   */
  isLoading(actionKey) {
    return this.getRequestState(actionKey).loading;
  }

  /**
   * Get error state for a specific action
   */
  getError(actionKey) {
    return this.getRequestState(actionKey).error;
  }
}

// Create default instance
export const apiClient = new APIClient();
