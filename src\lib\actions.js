'use server';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { authenticateUser, sendOTP, verifyOTP } from './api';

// Server action for login
export async function loginAction(formData) {
  try {
    const emailOrPhone = formData.get('emailOrPhone');
    const password = formData.get('password');

    if (!emailOrPhone || !password) {
      return {
        success: false,
        error: 'Email/phone and password are required',
      };
    }

    // Call server-side authentication API
    const result = await authenticateUser({
      emailOrPhone,
      password,
    });

    if (!result.success) {
      return {
        success: false,
        error: result.error || 'Authentication failed',
      };
    }

    // Store session data in secure HTTP-only cookies
    const cookieStore = await cookies();
    
    if (result.data.sessionToken) {
      cookieStore.set('session_token', result.data.sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });
    }

    if (result.data.userId) {
      cookieStore.set('user_id', result.data.userId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });
    }

    // If OTP is required, redirect to OTP page
    if (result.data.requiresOTP) {
      // Send OTP
      const otpResult = await sendOTP(result.data.phoneNumber, result.data.userId);
      
      if (otpResult.success) {
        // Store OTP ID for verification
        cookieStore.set('otp_id', otpResult.data.otpId, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 60 * 10, // 10 minutes
        });
        
        return {
          success: true,
          requiresOTP: true,
          phoneNumber: result.data.phoneNumber,
        };
      } else {
        return {
          success: false,
          error: 'Failed to send OTP',
        };
      }
    }

    return {
      success: true,
      requiresOTP: false,
    };
  } catch (error) {
    console.error('Login action error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred',
    };
  }
}

// Server action for OTP verification
export async function verifyOTPAction(formData) {
  try {
    const otpCode = formData.get('otpCode');

    if (!otpCode || otpCode.length !== 6) {
      return {
        success: false,
        error: 'Please enter a valid 6-digit OTP',
      };
    }

    const cookieStore = await cookies();
    const otpId = cookieStore.get('otp_id')?.value;
    const userId = cookieStore.get('user_id')?.value;

    if (!otpId || !userId) {
      return {
        success: false,
        error: 'OTP session expired. Please login again.',
      };
    }

    // Verify OTP server-side
    const result = await verifyOTP(otpCode, otpId, userId);

    if (!result.success) {
      return {
        success: false,
        error: result.error || 'OTP verification failed',
      };
    }

    // Store access tokens in secure cookies
    if (result.data.accessToken) {
      cookieStore.set('access_token', result.data.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60, // 1 hour
      });
    }

    if (result.data.refreshToken) {
      cookieStore.set('refresh_token', result.data.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 30, // 30 days
      });
    }

    // Clear OTP-related cookies
    cookieStore.delete('otp_id');

    return {
      success: true,
      user: result.data.user,
    };
  } catch (error) {
    console.error('OTP verification action error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred',
    };
  }
}

// Server action for resending OTP
export async function resendOTPAction() {
  try {
    const cookieStore = await cookies();
    const userId = cookieStore.get('user_id')?.value;
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!userId || !sessionToken) {
      return {
        success: false,
        error: 'Session expired. Please login again.',
      };
    }

    // Get user's phone number from session or API
    // For now, using a placeholder phone number
    const phoneNumber = '+1234567890'; // This should come from user session

    const result = await sendOTP(phoneNumber, userId);

    if (!result.success) {
      return {
        success: false,
        error: result.error || 'Failed to resend OTP',
      };
    }

    // Update OTP ID in cookies
    cookieStore.set('otp_id', result.data.otpId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 10, // 10 minutes
    });

    return {
      success: true,
      message: 'OTP has been resent successfully',
    };
  } catch (error) {
    console.error('Resend OTP action error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred',
    };
  }
}

// Server action for logout
export async function logoutAction() {
  try {
    const cookieStore = await cookies();
    
    // Clear all authentication cookies
    cookieStore.delete('session_token');
    cookieStore.delete('user_id');
    cookieStore.delete('access_token');
    cookieStore.delete('refresh_token');
    cookieStore.delete('otp_id');

    return {
      success: true,
    };
  } catch (error) {
    console.error('Logout action error:', error);
    return {
      success: false,
      error: 'Logout failed',
    };
  }
}

// Helper function to get current user session
export async function getCurrentUser() {
  try {
    const cookieStore = await cookies();
    const userId = cookieStore.get('user_id')?.value;
    const accessToken = cookieStore.get('access_token')?.value;

    if (!userId || !accessToken) {
      return null;
    }

    // In a real app, you would fetch user data from API
    // For now, return basic user info
    return {
      id: userId,
      // Add other user properties as needed
    };
  } catch (error) {
    console.error('Get current user error:', error);
    return null;
  }
}
