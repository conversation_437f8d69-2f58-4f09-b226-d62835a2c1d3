// API utility functions for server-side authentication
// These functions will be called server-side to hide sensitive API endpoints from the client

const API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8000';

// Server-side API client configuration
const createAPIClient = () => {
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'AuthApp/1.0',
  };

  // Add API key or other sensitive headers server-side only
  if (process.env.API_SECRET_KEY) {
    headers['Authorization'] = `Bearer ${process.env.API_SECRET_KEY}`;
  }

  return {
    headers,
    baseURL: API_BASE_URL,
  };
};

// Authentication API functions (server-side only)
export async function authenticateUser(credentials) {
  const client = createAPIClient();
  
  try {
    // Placeholder for actual API call
    const response = await fetch(`${client.baseURL}/auth/login`, {
      method: 'POST',
      headers: client.headers,
      body: JSON.stringify({
        emailOrPhone: credentials.emailOrPhone,
        password: credentials.password,
      }),
    });

    if (!response.ok) {
      throw new Error(`Authentication failed: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: {
        userId: data.userId,
        sessionToken: data.sessionToken,
        requiresOTP: data.requiresOTP,
        phoneNumber: data.phoneNumber,
      },
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Send OTP function (server-side only)
export async function sendOTP(phoneNumber, userId) {
  const client = createAPIClient();
  
  try {
    // Placeholder for actual API call
    const response = await fetch(`${client.baseURL}/auth/send-otp`, {
      method: 'POST',
      headers: client.headers,
      body: JSON.stringify({
        phoneNumber,
        userId,
      }),
    });

    if (!response.ok) {
      throw new Error(`OTP send failed: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: {
        otpId: data.otpId,
        expiresAt: data.expiresAt,
      },
    };
  } catch (error) {
    console.error('OTP send error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Verify OTP function (server-side only)
export async function verifyOTP(otpCode, otpId, userId) {
  const client = createAPIClient();
  
  try {
    // Placeholder for actual API call
    const response = await fetch(`${client.baseURL}/auth/verify-otp`, {
      method: 'POST',
      headers: client.headers,
      body: JSON.stringify({
        otpCode,
        otpId,
        userId,
      }),
    });

    if (!response.ok) {
      throw new Error(`OTP verification failed: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        user: data.user,
      },
    };
  } catch (error) {
    console.error('OTP verification error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Get user profile (server-side only)
export async function getUserProfile(userId, accessToken) {
  const client = createAPIClient();
  
  try {
    const response = await fetch(`${client.baseURL}/users/${userId}`, {
      method: 'GET',
      headers: {
        ...client.headers,
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user profile: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.user,
    };
  } catch (error) {
    console.error('User profile fetch error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Refresh token function (server-side only)
export async function refreshAccessToken(refreshToken) {
  const client = createAPIClient();
  
  try {
    const response = await fetch(`${client.baseURL}/auth/refresh`, {
      method: 'POST',
      headers: client.headers,
      body: JSON.stringify({
        refreshToken,
      }),
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
      },
    };
  } catch (error) {
    console.error('Token refresh error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// Utility function to validate environment variables
export function validateAPIConfiguration() {
  const requiredEnvVars = ['API_SECRET_KEY'];
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.warn(`Missing environment variables: ${missing.join(', ')}`);
    return false;
  }
  
  return true;
}
