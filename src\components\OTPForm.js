'use client';

import { useState, useRef, useEffect } from 'react';
import { useShiptAuth, formUtils } from '@/lib/shipt-api-adapter';
import { ErrorMessage } from './MessageContainer';

export default function OTPForm({ onSubmit, mfaData }) {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef([]);

  // Use Shipt authentication patterns
  const shiptAuth = useShiptAuth();

  useEffect(() => {
    // Focus on first input when component mounts
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  const handleInputChange = (index, value) => {
    // Only allow numeric input
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      handleOTPSubmit(newOtp.join(''));
    }
  };

  const handleKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace') {
      if (!otp[index] && index > 0) {
        // If current field is empty, focus previous field
        inputRefs.current[index - 1]?.focus();
      } else {
        // Clear current field
        const newOtp = [...otp];
        newOtp[index] = '';
        setOtp(newOtp);
      }
    }
    // Handle arrow keys
    else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    else if (e.key === 'ArrowRight' && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
    // Handle paste
    else if (e.key === 'Enter') {
      e.preventDefault();
      const otpString = otp.join('');
      if (otpString.length === 6) {
        onSubmit(otpString);
      }
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').slice(0, 6);
    
    if (digits.length === 6) {
      const newOtp = digits.split('');
      setOtp(newOtp);
      inputRefs.current[5]?.focus();
      handleOTPSubmit(digits);
    }
  };

  const handleOTPSubmit = async (otpCode) => {
    if (mfaData) {
      try {
        // Use Shipt MFA verification pattern
        const otpData = formUtils.transformOTPData(otpCode, mfaData.channel_id);
        const result = await shiptAuth.verifyMfa(otpData);

        // Call parent onSubmit with result
        if (onSubmit) {
          onSubmit(result);
          
        }
      } catch (error) {
        // Error is handled by shiptAuth hook
        console.error('Shipt OTP verification error:', error);
      }
    } else {
      // Fallback if no MFA data
      onSubmit(otpCode);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const otpString = otp.join('');

    // Use Shipt validation
    const validation = formUtils.validateOTPForm(otpString);
    if (validation.isValid) {
      handleOTPSubmit(otpString);
    }
  };

  const clearOTP = () => {
    setOtp(['', '', '', '', '', '']);
    inputRefs.current[0]?.focus();
  };

  return (
    <div className="animate-fade-in">
      <form onSubmit={handleSubmit} className="space-y-6">
        {shiptAuth.error && (
          <ErrorMessage message={shiptAuth.error?.message} />
        )}

        <div>
          <label className="label-text text-center mb-6 block">
            Enter 6-digit verification code
          </label>

          <div className="flex justify-center space-x-3 mb-6">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={el => inputRefs.current[index] = el}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={(e) => handleInputChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={handlePaste}
                className="otp-input"
                disabled={shiptAuth.loading}
                autoComplete="off"
              />
            ))}
          </div>

          <div className="text-center">
            <button
              type="button"
              onClick={clearOTP}
              className="text-sm text-foreground-light hover:text-foreground font-medium transition-colors duration-200"
              disabled={shiptAuth.loading}
            >
              Clear
            </button>
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={shiptAuth.loading || otp.join('').length !== 6}
            className="btn-primary"
          >
            {shiptAuth.loading ? (
              <div className="flex items-center justify-center">
                <div className="spinner mr-3"></div>
                Verifying...
              </div>
            ) : (
              'Verify Code'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
