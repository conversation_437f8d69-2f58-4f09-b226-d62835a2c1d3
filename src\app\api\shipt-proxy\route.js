// Next.js API Route: CORS Proxy for Shipt API calls
// This bypasses CORS restrictions by making server-side requests

import { NextResponse } from 'next/server';

// Shipt API headers (from Python backend)
const getShiptHeaders = () => ({
  'content-type': 'application/x-www-form-urlencoded',
  'x-shipt-deviceid': '7006BE60-E57F-4717-856E-7A112D6B4304',
  'accept': '*/*',
  'x-user-type': 'Driver',
  'accept-language': 'en-US,en;q=0.9',
  'accept-encoding': 'gzip, deflate, br',
  'x-shipt-geo-last-tracked': new Date().toISOString(),
  'x-shipt-geo-mocked': 'false',
  'user-agent': 'ShiptShopper/2227420 CFNetwork/3826.500.131 Darwin/24.5.0',
  'x-shipt-identifier': 'neutron-ios-4.98.0-8320',
  'x-shipt-geo-long': '-122.68234219244364',
  'x-shipt-geo-lat': '45.56178265826245'
});

// Endpoint mapping to hide real Shipt URLs
const ENDPOINT_MAP = {
  'oauth_token': 'https://api.shipt.com/auth/v3/oauth/token',
  'mfa_channels': 'https://api.shipt.com/auth/v1/oauth/mfa/channels',
  'send_mfa': (channelId) => `https://api.shipt.com/auth/v1/oauth/mfa/channels/${channelId}/codes`,
  'verify_mfa': (channelId, code, clientId) => `https://api.shipt.com/auth/v1/oauth/mfa/channels/${channelId}/codes/${code}?client_id=${clientId}`
};

export async function POST(request) {
  try {
    const body = await request.json();
    const { endpoint, method = 'POST', data, headers: extraHeaders = {}, params = {} } = body;

    console.log('[Shipt Proxy] Request:', { endpoint, method, data: data ? 'present' : 'none' });

    if (!endpoint) {
      return NextResponse.json({ error: 'Endpoint is required' }, { status: 400 });
    }

    // Map endpoint to actual URL
    let url;
    if (typeof ENDPOINT_MAP[endpoint] === 'function') {
      url = ENDPOINT_MAP[endpoint](...Object.values(params));
    } else if (ENDPOINT_MAP[endpoint]) {
      url = ENDPOINT_MAP[endpoint];
    } else {
      return NextResponse.json({ error: 'Invalid endpoint' }, { status: 400 });
    }

    // Prepare headers
    const headers = {
      ...getShiptHeaders(),
      ...extraHeaders
    };

    // Prepare request options
    const requestOptions = {
      method: method,
      headers: headers
    };

    // Add body for POST requests
    if (method === 'POST' && data) {
      if (typeof data === 'object' && !(data instanceof URLSearchParams)) {
        // Convert object to URLSearchParams for form data
        const formData = new URLSearchParams();
        Object.keys(data).forEach(key => {
          formData.append(key, data[key]);
        });
        requestOptions.body = formData;
      } else {
        requestOptions.body = data;
      }
    }

    console.log('[Shipt Proxy] Making request to:', url);

    // Make the actual request to Shipt API
    const response = await fetch(url, requestOptions);
    
    console.log('[Shipt Proxy] Response status:', response.status);

    // Get response data
    let responseData;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    // Return the response with CORS headers
    const nextResponse = NextResponse.json({
      success: response.ok,
      status: response.status,
      data: responseData,
      headers: Object.fromEntries(response.headers.entries())
    }, { 
      status: response.ok ? 200 : response.status 
    });

    // Add CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*');
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return nextResponse;

  } catch (error) {
    console.error('[Shipt Proxy] Error:', error);
    
    const errorResponse = NextResponse.json({
      success: false,
      error: error.message,
      details: error.stack
    }, { status: 500 });

    // Add CORS headers even for errors
    errorResponse.headers.set('Access-Control-Allow-Origin', '*');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return errorResponse;
  }
}

// Handle preflight requests
export async function OPTIONS(request) {
  const response = new NextResponse(null, { status: 200 });
  
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Max-Age', '86400');
  
  return response;
}

// Also handle GET requests for MFA channels
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');
    const authHeader = searchParams.get('auth');

    console.log('[Shipt Proxy GET] Request:', { endpoint, auth: authHeader ? 'present' : 'none' });

    if (!endpoint) {
      return NextResponse.json({ error: 'Endpoint is required' }, { status: 400 });
    }

    // Map endpoint to actual URL
    const url = ENDPOINT_MAP[endpoint];
    if (!url) {
      return NextResponse.json({ error: 'Invalid endpoint' }, { status: 400 });
    }

    // Prepare headers
    const headers = {
      ...getShiptHeaders()
    };

    // Add authorization header if provided
    if (authHeader) {
      headers['authorization'] = authHeader;
    }

    console.log('[Shipt Proxy GET] Making request to:', url);

    // Make the actual request to Shipt API
    const response = await fetch(url, {
      method: 'GET',
      headers: headers
    });
    
    console.log('[Shipt Proxy GET] Response status:', response.status);

    // Get response data
    let responseData;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    // Return the response with CORS headers
    const nextResponse = NextResponse.json({
      success: response.ok,
      status: response.status,
      data: responseData,
      headers: Object.fromEntries(response.headers.entries())
    }, { 
      status: response.ok ? 200 : response.status 
    });

    // Add CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*');
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return nextResponse;

  } catch (error) {
    console.error('[Shipt Proxy GET] Error:', error);
    
    const errorResponse = NextResponse.json({
      success: false,
      error: error.message,
      details: error.stack
    }, { status: 500 });

    // Add CORS headers even for errors
    errorResponse.headers.set('Access-Control-Allow-Origin', '*');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return errorResponse;
  }
}
