/**
 * Reusable message container components for displaying errors, success, warnings, and info messages
 * with consistent styling and proper padding
 */

/**
 * Error message container component
 */
export function ErrorMessage({ message, children, className = '' }) {
  if (!message && !children) return null;

  return (
    <div className={`error-container ${className}`}>
      <div className="error-content">
        <svg className="error-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
        <div className="error-text">
          {message && <p>{message}</p>}
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * Success message container component
 */
export function SuccessMessage({ message, children, className = '' }) {
  if (!message && !children) return null;

  return (
    <div className={`success-container ${className}`}>
      <div className="success-content">
        <svg className="success-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
        <div className="success-text">
          {message && <p>{message}</p>}
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * Warning message container component
 */
export function WarningMessage({ message, children, className = '' }) {
  if (!message && !children) return null;

  return (
    <div className={`warning-container ${className}`}>
      <div className="warning-content">
        <svg className="warning-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <div className="warning-text">
          {message && <p>{message}</p>}
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * Info message container component
 */
export function InfoMessage({ message, children, className = '' }) {
  if (!message && !children) return null;

  return (
    <div className={`info-container ${className}`}>
      <div className="info-content">
        <svg className="info-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
        <div className="info-text">
          {message && <p>{message}</p>}
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * Generic message container that can display different types based on the type prop
 */
export function MessageContainer({ type = 'info', message, children, className = '' }) {
  switch (type) {
    case 'error':
      return <ErrorMessage message={message} className={className}>{children}</ErrorMessage>;
    case 'success':
      return <SuccessMessage message={message} className={className}>{children}</SuccessMessage>;
    case 'warning':
      return <WarningMessage message={message} className={className}>{children}</WarningMessage>;
    case 'info':
    default:
      return <InfoMessage message={message} className={className}>{children}</InfoMessage>;
  }
}
