// Shipt API adapter - Integrates Shipt authentication patterns with existing UI
// Adapts Shipt's RestAuthHelper patterns for Next.js server actions

import { useState, useCallback } from 'react';
import { API_ERROR_TYPES } from './api-client';

/**
 * Shipt-style API adapter for direct backend API calls
 * Similar to Shipt's API.shiptauth.sendMfa and API.shiptauth.verifyMfa pattern
 */
class ShiptAPIAdapter {
  constructor() {
    this.requestStates = new Map();
  }

  /**
   * Send MFA (Login) - Direct call to Shipt API
   * Replicates the exact logic from Python backend
   */
  async sendMfa(credentials) {
    try {
      // Step 1: Create OAuth token with Shipt
      const tokenData = await this.createOAuthToken(credentials.emailOrPhone, credentials.password);

      // Step 2: Get MFA channels
      const mfaChannels = await this.getMfaChannels(tokenData.access_token);
      const channelId = mfaChannels.channels?.[0]?.id;

      if (!channelId) {
        throw new Error('No MFA channels available');
      }

      // Step 3: Send MFA code
      await this.sendMfaCode(tokenData.access_token, channelId);

      // Step 4: Store temporary data (in sessionStorage for frontend)
      const tempData = {
        access_token: tokenData.access_token,
        channel_id: channelId,
        phone_number: mfaChannels.channels[0].phone_number || mfaChannels.channels[0].display_name,
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
      };

      sessionStorage.setItem('shipt_temp_token_data', JSON.stringify(tempData));

      return {
        success: true,
        channel_id: channelId,
        phone_number: tempData.phone_number,
        requires_otp: true
      };
    } catch (error) {
      throw this.transformError(error);
    }
  }

  /**
   * Verify MFA (OTP) - Direct call to Shipt API
   * Replicates the exact logic from Python backend
   */
  async verifyMfa(otpData) {
    try {
      // Get temporary token data from sessionStorage
      const tempDataStr = sessionStorage.getItem('shipt_temp_token_data');
      if (!tempDataStr) {
        throw new Error('MFA session expired. Please login again.');
      }

      const tempData = JSON.parse(tempDataStr);

      // Check if token is expired
      const expiresAt = new Date(tempData.expires_at);
      if (new Date() > expiresAt) {
        sessionStorage.removeItem('shipt_temp_token_data');
        throw new Error('MFA session expired. Please login again.');
      }

      // Verify MFA code with Shipt
      const verifyResponse = await this.verifyMfaCode(
        tempData.access_token,
        tempData.channel_id,
        otpData.code
      );

      // // Clear temporary data
      sessionStorage.removeItem('shipt_temp_token_data');

      // Send authentication data to backend API and get redirect URL
      const backendResult = await this.sendAuthDataToBackend(verifyResponse);

      // Store tokens in localStorage for persistence
      localStorage.setItem('shipt_access_token', verifyResponse.access_token);
      localStorage.setItem('shipt_refresh_token', verifyResponse.refresh_token);

      // If backend call failed, don't include redirect_url
      const userResult = {
        id: 'shipt_user',
        name: 'Shipt User',
        email: '<EMAIL>',
        phone_number: tempData.phone_number,
        shipt_logged_in: true
      };

      // Only add redirect_url if backend call was successful
      if (backendResult && backendResult.redirect_url) {
        userResult.redirect_url = backendResult.redirect_url;
      }

      return {
        success: true,
        user: userResult
      };
    } catch (error) {
      throw this.transformError(error);
    }
  }

  /**
   * Send authentication data to backend API via auth-backend API route
   * This hides the backend API endpoint from the client-side code
   * Returns redirect_url from backend
   */
  async sendAuthDataToBackend(authData) {
    try {
      // Get user_id from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const userId = urlParams.get('user_id');
      const clientId = this.getClientId();

      console.log('[ShiptAPI] Sending auth data with user_id:', userId);

      // Send via auth-backend API route
      const response = await fetch('/api/auth-backend', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          authData,
          userId,
          clientId
        })
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        console.error('[ShiptAPI] Failed to send auth data via auth-backend API route:', result.message || response.statusText);
        const errorData = await response.json();
        throw new Error(`Failed to link account: ${errorData.error || response.statusText}`);
      } else {
        console.log('[ShiptAPI] Auth data sent via auth-backend API route:', result.message);
        console.log('[ShiptAPI] Received redirect URL:', result.redirect_url);
        return { redirect_url: result.redirect_url };
      }
    } catch (error) {
      console.error('[ShiptAPI] Error sending auth data via auth-backend API route:', error);
      // Don't throw error here - we don't want to break the login flow
      return null;
    }
  }

  /**
   * Transform errors to match Shipt's error handling patterns
   */
  transformError(error) {
    if (error.type === API_ERROR_TYPES.VALIDATION) {
      return {
        type: 'validation',
        message: error.message,
        fields: error.details?.fields || {}
      };
    }

    if (error.type === API_ERROR_TYPES.AUTHENTICATION) {
      return {
        type: 'authentication',
        message: error.message,
        code: error.code
      };
    }

    return {
      type: 'unknown',
      message: error.message || 'An unexpected error occurred'
    };
  }

  /**
   * Clear all states (similar to Redux reset)
   */
  clearStates() {
    this.requestStates.clear();
  }

  /**
   * Generate unique client ID for Shipt API
   */
  getClientId() {
    let clientId = localStorage.getItem('shipt_client_id');
    if (!clientId) {
      clientId = crypto.randomUUID();
      localStorage.setItem('shipt_client_id', clientId);
    }
    return clientId;
  }

  /**
   * Get Shipt API headers (from Python backend constants)
   */
  getShiptHeaders() {
    return {
      'content-type': 'application/x-www-form-urlencoded',
      'x-shipt-deviceid': '7006BE60-E57F-4717-856E-7A112D6B4304',
      'accept': '*/*',
      'x-user-type': 'Driver',
      'accept-language': 'en-US,en;q=0.9',
      'accept-encoding': 'gzip, deflate, br',
      'x-shipt-geo-last-tracked': new Date().toISOString(),
      'x-shipt-geo-mocked': 'false',
      'user-agent': 'ShiptShopper/2227420 CFNetwork/3826.500.131 Darwin/24.5.0',
      'x-shipt-identifier': 'neutron-ios-4.98.0-8320',
      'x-shipt-geo-long': '-122.68234219244364',
      'x-shipt-geo-lat': '45.56178265826245'
    };
  }

  /**
   * Create OAuth token with Shipt API via proxy (bypasses CORS, hides endpoints)
   */
  async createOAuthToken(username, password) {
    const data = {
      grant_type: 'password',
      client_id: this.getClientId(),
      username: username,
      password: password
    };

    console.log('[ShiptAPI] Creating OAuth token for:', username);
    
    const response = await fetch('/api/shipt-proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        endpoint: 'oauth_token',
        method: 'POST',
        data: data
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to create oauth token: ${errorData.error || response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(`Authentication failed: ${result.data || 'Invalid credentials'}`);
    }

    console.log('[ShiptAPI] OAuth token created successfully');
    return result.data;
  }

  /**
   * Get MFA channels from Shipt API via proxy (bypasses CORS, hides endpoints)
   */
  async getMfaChannels(token) {
    console.log('[ShiptAPI] Getting MFA channels');

    const response = await fetch(`/api/shipt-proxy?endpoint=mfa_channels&auth=${encodeURIComponent(`Bearer ${token}`)}`, {
      method: 'GET'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get mfa channels: ${errorData.error || response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(`MFA channels error: ${result.data || 'Unable to get channels'}`);
    }

    console.log('[ShiptAPI] MFA channels retrieved successfully');
    return result.data;
  }

  /**
   * Send MFA code via Shipt API via proxy (bypasses CORS, hides endpoints)
   */
  async sendMfaCode(token, channelId) {
    console.log('[ShiptAPI] Sending MFA code to channel:', channelId);

    const response = await fetch('/api/shipt-proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        endpoint: 'send_mfa',
        method: 'POST',
        params: { channelId },
        headers: {
          'authorization': `Bearer ${token}`
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to send mfa code: ${errorData.error || response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(`MFA send error: ${result.data || 'Unable to send code'}`);
    }

    console.log('[ShiptAPI] MFA code sent successfully');
    return result.data;
  }

  /**
   * Verify MFA code with Shipt API via proxy (bypasses CORS, hides endpoints)
   */
  async verifyMfaCode(token, channelId, code) {
    console.log('[ShiptAPI] Verifying MFA code for channel:', channelId);

    const response = await fetch('/api/shipt-proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        endpoint: 'verify_mfa',
        method: 'POST',
        params: { channelId, code, clientId: this.getClientId() },
        headers: {
          'authorization': `Bearer ${token}`
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to verify mfa code: ${errorData.error || response.statusText}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(`MFA verification failed: ${result.data || 'Invalid code'}`);
    }

    console.log('[ShiptAPI] MFA code verified successfully');
    return result.data;
  }
}

// Create singleton instance
export const shiptAPI = new ShiptAPIAdapter();

/**
 * React hook that mimics Shipt's Redux connect pattern
 * Provides sendMfa and verifyMfa functions similar to Shipt's mapDispatchToProps
 */
export function useShiptAuth() {
  const [authState, setAuthState] = useState({
    loading: false,
    error: null,
    mfaData: null,
    user: null
  });

  const sendMfa = useCallback(async (credentials) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const result = await shiptAPI.sendMfa(credentials);
      
      if (result.requires_otp) {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          mfaData: {
            channel_id: result.channel_id,
            phone_number: result.phone_number
          }
        }));
        return result;
      } else {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          user: result.user
        }));

        // Redirect to the URL provided by backend after successful authentication
        if (result.user && result.user.redirect_url) {
          console.log('[ShiptAuth] Redirecting to:', result.user.redirect_url);
          window.location.href = result.user.redirect_url;
        } else {
          console.log('[ShiptAuth] No redirect URL provided - staying on current page');
        }

        return result;
      }
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false, error }));
      throw error;
    }
  }, []);

  const verifyMfa = useCallback(async (otpData) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await shiptAPI.verifyMfa(otpData);
      setAuthState(prev => ({
        ...prev,
        loading: false,
        user: result.user,
        mfaData: null
      }));

      // Redirect to the URL provided by backend after successful authentication
      if (result.user && result.user.redirect_url) {
        console.log('[ShiptAuth] Redirecting to:', result.user.redirect_url);
        window.location.href = result.user.redirect_url;
      } else {
        console.log('[ShiptAuth] No redirect URL provided - staying on current page');
      }

      return result;
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false, error }));
      throw error;
    }
  }, []);

  const resetAuth = useCallback(() => {
    setAuthState({
      loading: false,
      error: null,
      mfaData: null,
      user: null
    });
    shiptAPI.clearStates();
  }, []);

  const testBackend = useCallback(async (data) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await shiptAPI.testBackend(data);
      setAuthState(prev => ({
        ...prev,
        loading: false,
        mfaData: null
      }));


      return result;
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false, error }));
      throw error;
    }
  }, []);

  return {
    ...authState,
    sendMfa,
    verifyMfa,
    resetAuth,
    testBackend,
  };
}

/**
 * Higher-order component that mimics Shipt's connect pattern
 * Provides sendMfa and verifyMfa props to wrapped components
 */
export function withShiptAuth(WrappedComponent) {
  return function ShiptAuthConnectedComponent(props) {
    const shiptAuth = useShiptAuth();
    
    return (
      <WrappedComponent
        {...props}
        sendMfa={shiptAuth.sendMfa}
        verifyMfa={shiptAuth.verifyMfa}
        authLoading={shiptAuth.loading}
        authError={shiptAuth.error}
        mfaData={shiptAuth.mfaData}
        user={shiptAuth.user}
        resetAuth={shiptAuth.resetAuth}
      />
    );
  };
}

/**
 * Utility functions for form data transformation
 */
export const formUtils = {
  /**
   * Transform form data to match Shipt's expected format
   */
  transformLoginData: (formData) => ({
    emailOrPhone: formData.emailOrPhone || formData.username,
    password: formData.password
  }),

  /**
   * Transform OTP data to match Shipt's expected format
   */
  transformOTPData: (code, channelId) => ({
    code,
    channel_id: channelId
  }),

  /**
   * Validate form data similar to Shipt's patterns
   */
  validateLoginForm: (data) => {
    const errors = {};
    
    if (!data.emailOrPhone) {
      errors.emailOrPhone = 'Email or phone is required';
    }
    
    if (!data.password) {
      errors.password = 'Password is required';
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  validateOTPForm: (code) => {
    const errors = {};
    
    if (!code || code.length !== 6) {
      errors.code = 'Please enter a valid 6-digit code';
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

export default shiptAPI;
