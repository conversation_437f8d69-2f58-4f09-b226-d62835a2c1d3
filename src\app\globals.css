@import "tailwindcss";

:root {
  --background: #eef2f6;
  --background-solid: #f8f9fa;
  --foreground: #2c3e50;
  --foreground-light: #6c757d;
  --primary: #23cc6b;
  --primary-dark: #00a041;
  --primary-light: #00e65a;
  --secondary: #6c757d;
  --accent: #17a2b8;
  --error: #dc3545;
  --success: #28a745;
  --warning: #ffc107;
  --border: #dee2e6;
  --border-light: #f8f9fa;
  --input-bg: #ffffff;
  --card-bg: #ffffff;
  --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
  --radius: 1rem;
  --radius-sm: 0.75rem;
}

@theme inline {
  --color-background: var(--background-solid);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-error: var(--error);
  --color-success: var(--success);
  --color-border: var(--border);
  --color-input-bg: var(--input-bg);
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
}

/* Layout utilities */
.container-mobile {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 1.5rem;
}

.auth-container {
  min-height: 100vh;
  background: var(--background);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.25rem;
}

.auth-card {
  background: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 2rem 1.5rem;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* Form input styles */
.input-field {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid var(--border);
  border-radius: var(--radius-sm);
  background: var(--input-bg);
  font-size: 1rem;
  font-weight: 400;
  color: var(--foreground);
  transition: all 0.3s ease;
  font-family: inherit;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.input-field:hover {
  border-color: var(--primary-light);
}

.input-field.error {
  border-color: var(--error);
  box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.1);
}

/* Button styles */
.btn-primary {
  width: 100%;
  padding: 1rem 1.5rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: none;
  position: relative;
  overflow: hidden;
  min-height: 48px;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: none;
}

.btn-primary:active {
  background: var(--primary-dark);
  transform: scale(0.98);
}

.btn-primary:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  width: 100%;
  padding: 1rem 1.5rem;
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  border-radius: var(--radius-sm);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-1px);
}

/* Typography */
.heading-primary {
  font-size: 2rem;
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: 0.5rem;
  text-align: center;
}

.heading-secondary {
  font-size: 1.125rem;
  font-weight: 400;
  color: var(--foreground-light);
  margin-bottom: 2rem;
  text-align: center;
  line-height: 1.5;
}

.label-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground);
  margin-bottom: 0.5rem;
  display: block;
}

/* OTP input styles */
.otp-input {
  width: 3.5rem;
  height: 3.5rem;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  border: 2px solid var(--border);
  border-radius: var(--radius-sm);
  background: var(--input-bg);
  transition: all 0.3s ease;
}

.otp-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: scale(1.05);
}

/* Error message container styles */
.error-container {
  background: #fef2f2;
  border: 2px solid #fecaca;
  border-radius: 0.75rem;
  padding: 1.25rem 1.5rem;
  margin-bottom: 1.5rem;
  animation: slide-in 0.3s ease-out;
}

.error-container .error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-container .error-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #dc2626;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-container .error-text {
  font-size: 0.9rem;
  color: #b91c1c;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
}

/* Success message container styles */
.success-container {
  background: #f0fdf4;
  border: 2px solid #bbf7d0;
  border-radius: 0.75rem;
  padding: 1.25rem 1.5rem;
  margin-bottom: 1.5rem;
  animation: slide-in 0.3s ease-out;
}

.success-container .success-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.success-container .success-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #16a34a;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.success-container .success-text {
  font-size: 0.9rem;
  color: #15803d;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
}

/* Warning message container styles */
.warning-container {
  background: #fffbeb;
  border: 2px solid #fed7aa;
  border-radius: 0.75rem;
  padding: 1.25rem 1.5rem;
  margin-bottom: 1.5rem;
  animation: slide-in 0.3s ease-out;
}

.warning-container .warning-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.warning-container .warning-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #d97706;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.warning-container .warning-text {
  font-size: 0.9rem;
  color: #92400e;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
}

/* Info message container styles */
.info-container {
  background: #eff6ff;
  border: 2px solid #bfdbfe;
  border-radius: 0.75rem;
  padding: 1.25rem 1.5rem;
  margin-bottom: 1.5rem;
  animation: slide-in 0.3s ease-out;
}

.info-container .info-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.info-container .info-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #2563eb;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.info-container .info-text {
  font-size: 0.9rem;
  color: #1d4ed8;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
}

/* Legacy error message styles (for inline field errors) */
.error-message {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Legacy success message styles (for inline field messages) */
.success-message {
  color: var(--success);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
    border-radius: var(--radius-sm);
  }

  .heading-primary {
    font-size: 1.75rem;
  }

  .heading-secondary {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .error-container,
  .success-container,
  .warning-container,
  .info-container {
    padding: 1rem 1.25rem;
    margin-bottom: 1.25rem;
  }

  .error-container .error-text,
  .success-container .success-text,
  .warning-container .warning-text,
  .info-container .info-text {
    font-size: 0.875rem;
  }
}

@media (max-width: 640px) {
  .container-mobile {
    padding: 1rem;
  }

  .auth-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .input-field {
    padding: 1rem;
    font-size: max(1rem, 16px); /* Prevent zoom on iOS */
  }

  .btn-primary, .btn-secondary {
    padding: 1rem;
    font-size: 1rem;
    min-height: 48px; /* Minimum touch target size */
  }

  .otp-input {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
    min-width: 44px;
    min-height: 44px;
  }

  .heading-primary {
    font-size: 1.5rem;
  }

  .heading-secondary {
    font-size: 0.9rem;
  }

  .error-container,
  .success-container,
  .warning-container,
  .info-container {
    padding: 0.875rem 1rem;
    margin-bottom: 1rem;
  }

  .error-container .error-content,
  .success-container .success-content,
  .warning-container .warning-content,
  .info-container .info-content {
    gap: 0.5rem;
  }

  .error-container .error-text,
  .success-container .success-text,
  .warning-container .warning-text,
  .info-container .info-text {
    font-size: 0.8rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    transform: none;
  }

  .btn-secondary:hover {
    background: transparent;
    transform: none;
  }

  .input-field:hover {
    border-color: var(--border);
    transform: none;
  }

  .otp-input:focus {
    transform: none;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .input-field {
    border-width: 2px;
  }

  .btn-primary {
    border: 2px solid transparent;
  }

  .otp-input {
    border-width: 2px;
  }
}

/* Focus visible for keyboard navigation */
.input-field:focus-visible,
.btn-primary:focus-visible,
.otp-input:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  body {
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
  }
}

/* Logo styles */
.logo {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 20px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  font-weight: bold;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Loading spinner */
.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Additional responsive improvements */
@media (max-width: 480px) {
  .auth-card {
    padding: 1.25rem;
    margin: 0.25rem;
  }

  .heading-primary {
    font-size: 1.375rem;
  }

  .heading-secondary {
    font-size: 0.875rem;
    margin-bottom: 1.25rem;
  }

  .input-field {
    padding: 0.875rem 1rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.875rem 1rem;
    min-height: 44px;
  }

  .otp-input {
    width: 2.75rem;
    height: 2.75rem;
    font-size: 1.125rem;
  }

  .error-container,
  .success-container,
  .warning-container,
  .info-container {
    padding: 0.75rem 0.875rem;
    margin-bottom: 0.875rem;
    border-radius: 0.5rem;
  }

  .error-container .error-icon,
  .success-container .success-icon,
  .warning-container .warning-icon,
  .info-container .info-icon {
    width: 1rem;
    height: 1rem;
  }

  .error-container .error-text,
  .success-container .success-text,
  .warning-container .warning-text,
  .info-container .info-text {
    font-size: 0.75rem;
    line-height: 1.4;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    --background-solid: #1a202c;
    --foreground: #f7fafc;
    --foreground-light: #e2e8f0;
    --card-bg: #2d3748;
    --input-bg: #4a5568;
    --border: #4a5568;
    --border-light: #2d3748;
  }
}

/* Print styles */
@media print {
  .auth-container {
    background: white !important;
  }

  .auth-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
