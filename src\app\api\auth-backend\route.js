// Next.js API route to handle sending auth data to backend
// This hides the backend API endpoint from the client-side code

import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Parse the request body
    const body = await request.json();
    const { authData, userId, clientId } = body;

    // Validate required data
    if (!authData) {
      return NextResponse.json(
        { success: false, error: 'Auth data is required' },
        { status: 400 }
      );
    }

    // Prepare the payload for the backend API
    const payload = {
      data: {
        ...authData,
        client_id: clientId,
        user_id: userId, // Add user_id to the auth data,
        key: process.env.SECRET_KEY || null
      },
      headers: {
        'content-type': 'application/json',
        'date': new Date().toUTCString()
      }
    };

    console.log('[Auth Backend API] Sending payload to backend with user_id:', userId);

    // Get backend URL from environment variables
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';

    // Make the request to the real backend API
    const requestUrl = `${backendUrl}/api/vb/auth0/shipt/`;
    const requestBody = JSON.stringify(payload);

    const backendResponse = await fetch(requestUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: requestBody
    });

    if (!backendResponse.ok) {
      console.error('[Auth Backend API] Backend API error:', backendResponse.statusText);
      
      // Return error when backend fails - don't include redirect_url
      return NextResponse.json({
        success: false,
        message: backendResponse.statusText,
      }, { status: backendResponse.status });
    }

    const backendResult = await backendResponse.json();
    console.log('[Auth Backend API] Successfully sent auth data to backend');

    // Get redirect URL from environment variables
    const redirectUrl = process.env.REDIRECT_URL || 'http://localhost:8001/dashboard';

    // Return success response with redirect URL
    return NextResponse.json({
      success: true,
      message: 'Auth data sent to backend successfully',
      backendStatus: 'success',
      redirect_url: redirectUrl,
      backendData: backendResult
    });

  } catch (error) {
    console.error('[Auth Backend API] Error processing auth data:', error);
    
    // Return error when there's an error - don't include redirect_url
    return NextResponse.json({
      success: false,
      message: 'Error processing auth data',
      backendStatus: 'error',
      error: error.message
    }, { status: 500 });
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
